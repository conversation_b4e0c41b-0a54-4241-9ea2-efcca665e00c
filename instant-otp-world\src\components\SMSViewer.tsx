import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  MessageSquare,
  Refresh<PERSON><PERSON>,
  <PERSON><PERSON>,
  CheckCircle,
  Loader2,
  Clock,
  Smartphone,
} from "lucide-react";
import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { type Message } from "@/lib/api";

interface SMSViewerProps {
  messages: Message[];
  isLoading: boolean;
}

export const SMSViewer = ({ messages, isLoading }: SMSViewerProps) => {
  const [copiedCodes, setCopiedCodes] = useState<Set<string>>(new Set());
  const { toast } = useToast();

  const handleCopyCode = async (message: Message) => {
    try {
      await navigator.clipboard.writeText(message.code);
      setCopiedCodes((prev) => new Set([...prev, message.id]));
      toast({
        title: "Code copied!",
        description: `Verification code copied to clipboard`,
      });
      setTimeout(() => {
        setCopiedCodes((prev) => {
          const newSet = new Set(prev);
          newSet.delete(message.id);
          return newSet;
        });
      }, 2000);
    } catch (err) {
      toast({
        title: "Failed to copy",
        description: "Please copy the code manually",
        variant: "destructive",
      });
    }
  };

  const formatTime = (timeStr: string) => {
    const date = new Date(timeStr);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  return (
    <div>
      {isLoading ? (
        <div className="text-center py-12">
          <div className="p-4 bg-gradient-to-r from-orange-100 to-yellow-100 dark:from-orange-900/20 dark:to-yellow-900/20 rounded-2xl w-fit mx-auto mb-6">
            <Loader2 className="w-12 h-12 text-orange-600 animate-spin" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Checking for Messages</h3>
          <p className="text-muted-foreground">
            Waiting for SMS messages to arrive...
          </p>
        </div>
      ) : messages.length === 0 ? (
        <div className="text-center py-12">
          <div className="p-4 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-2xl w-fit mx-auto mb-6">
            <MessageSquare className="w-12 h-12 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold mb-2">No Messages Yet</h3>
          <p className="text-muted-foreground">
            SMS messages will appear here when they arrive
          </p>
          <div className="mt-4 text-sm text-muted-foreground">
            <p>💡 Tip: Messages usually arrive within 30 seconds</p>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Messages Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <h3 className="font-semibold">Received Messages</h3>
              <Badge className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800">
                {messages.length} message{messages.length !== 1 ? "s" : ""}
              </Badge>
            </div>
          </div>

          {/* Messages List */}
          <div className="space-y-3">
            {messages.map((message, index) => (
              <div
                key={message.id}
                className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-5 border border-blue-200/50 dark:border-blue-800/50 transition-all duration-200 hover:shadow-md"
              >
                {/* Message Header */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg">
                      <Smartphone className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant="outline"
                          className="bg-white/80 border-blue-200"
                        >
                          {message.sender}
                        </Badge>
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Clock className="w-3 h-3" />
                          {formatTime(message.time)}
                        </div>
                      </div>
                      <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                        Message #{messages.length - index}
                      </p>
                    </div>
                  </div>
                  <Button
                    onClick={() => handleCopyCode(message)}
                    variant="outline"
                    size="sm"
                    className={`transition-all duration-200 ${
                      copiedCodes.has(message.id)
                        ? "bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
                        : "bg-white/80 hover:bg-white"
                    }`}
                  >
                    {copiedCodes.has(message.id) ? (
                      <>
                        <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="w-4 h-4 mr-2" />
                        Copy Code
                      </>
                    )}
                  </Button>
                </div>

                {/* Message Content */}
                <div className="space-y-3">
                  <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg p-3">
                    <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                      {message.text}
                    </p>
                  </div>

                  {/* Verification Code */}
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30 rounded-lg p-4 border border-green-200/50 dark:border-green-800/50">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-green-800 dark:text-green-200 mb-1">
                          Verification Code:
                        </p>
                        <code className="text-2xl font-mono font-bold text-green-900 dark:text-green-100 tracking-wider">
                          {message.code}
                        </code>
                      </div>
                      <div className="text-right">
                        <Badge className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800">
                          Ready to use
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
