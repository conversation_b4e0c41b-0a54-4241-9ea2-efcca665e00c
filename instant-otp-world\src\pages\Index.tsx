import { useState, useEffect } from "react";
import { CountrySelector } from "@/components/CountrySelector";
import { PhoneNumberDisplay } from "@/components/PhoneNumberDisplay";
import { SMSViewer } from "@/components/SMSViewer";
import { Footer } from "@/components/Footer";
import {
  RefreshCw,
  Shield,
  Zap,
  Globe,
  Smartphone,
  Clock,
  Star,
  Users,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import axios, { AxiosError } from "axios";
import type { AxiosResponse } from "axios";
import {
  fetchCountries,
  fetchOperators,
  createOrder,
  fetchMessages,
  type Country,
  type OrderResponse,
  type Operator,
  type Message,
} from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";

const Index = () => {
  const { toast } = useToast();
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [selectedOperator, setSelectedOperator] = useState<string | null>(null);
  const [currentOrder, setCurrentOrder] = useState<OrderResponse | null>(null);
  const queryClient = useQueryClient();

  // Fetch operators when country changes
  const { data: operators, isLoading: isLoadingOperators } = useQuery({
    queryKey: ["operators", selectedCountry],
    queryFn: () =>
      selectedCountry ? fetchOperators(selectedCountry) : Promise.resolve([]),
    enabled: !!selectedCountry,
  });

  // Fetch countries
  const { data: countries, isLoading: isLoadingCountries } = useQuery({
    queryKey: ["countries"],
    queryFn: fetchCountries,
  });

  // Fetch messages when there's an active order
  const { data: messages = [], isLoading: isLoadingMessages } = useQuery({
    queryKey: ["messages", currentOrder?.id],
    queryFn: () =>
      currentOrder?.id ? fetchMessages(currentOrder.id) : Promise.resolve([]),
    enabled: !!currentOrder?.id,
    refetchInterval: 5000, // Poll every 5 seconds
    refetchIntervalInBackground: true,
  });

  // Create order mutation
  const orderMutation = useMutation<
    OrderResponse,
    AxiosError,
    { country: string; operator: string; product: string }
  >({
    mutationFn: (params) => createOrder(params),
    onSuccess: (data) => {
      setCurrentOrder(data);
      toast({
        title: "Success",
        description: "Successfully purchased phone number",
      });
    },
    onError: (error: AxiosError) => {
      // Show a more detailed error message to the user
      let errorMessage = "Failed to purchase phone number. Please try again.";
      if (
        error.response?.data &&
        typeof error.response.data === "object" &&
        "error" in error.response.data
      ) {
        errorMessage = (error.response.data as { error: string }).error;
      } else if (
        error.response?.data &&
        typeof error.response.data === "object" &&
        "message" in error.response.data
      ) {
        errorMessage = (error.response.data as { message: string }).message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const handleCountrySelect = (countryId: string) => {
    setSelectedCountry(countryId);
    setSelectedOperator(null);
    setCurrentOrder(null);
  };

  const handleOperatorSelect = (operatorId: string) => {
    setSelectedOperator(operatorId);
  };

  const handlePurchase = async () => {
    if (!selectedCountry || !selectedOperator) {
      toast({
        title: "Error",
        description: "Please select both country and operator",
        variant: "destructive",
      });
      return;
    }

    try {
      // Get the selected operator's data
      const operator = operators?.find((op) => op.id === selectedOperator);

      if (!operator || !operator.products?.length) {
        throw new Error("No products available for the selected operator");
      }

      // Use the first available product
      const product = operator.products[0];

      const orderParams = {
        country: selectedCountry,
        operator: selectedOperator,
        product: product,
      };

      // Proceed with the mutation
      orderMutation.mutate(orderParams);
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to place order",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10 dark:from-blue-400/5 dark:to-purple-400/5"></div>
        <div className="container mx-auto px-4 py-16 relative">
          <div className="text-center max-w-4xl mx-auto">
            <div className="flex items-center justify-center mb-6">
              <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-lg">
                <Smartphone className="w-8 h-8 text-white" />
              </div>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
              Instant OTP World
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Get instant virtual phone numbers for OTP verification from 160+
              countries. Fast, secure, and reliable SMS reception in seconds.
            </p>
            <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4 text-blue-500" />
                <span>160+ Countries</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-green-500" />
                <span>Instant Delivery</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4 text-purple-500" />
                <span>100% Secure</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid gap-8 md:grid-cols-2">
          <div className="space-y-6">
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                    <Globe className="w-5 h-5 text-white" />
                  </div>
                  Select Location
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CountrySelector
                  countries={countries || {}}
                  selectedCountry={selectedCountry}
                  onSelect={handleCountrySelect}
                  isLoading={isLoadingCountries}
                />
              </CardContent>
            </Card>

            {selectedCountry && (
              <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3 text-xl">
                    <div className="p-2 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg">
                      <Smartphone className="w-5 h-5 text-white" />
                    </div>
                    Select Operator
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    {operators?.length === 0 ? (
                      <div className="text-sm text-muted-foreground p-2 border rounded bg-muted">
                        No operators available for the selected country.
                      </div>
                    ) : (
                      <Select
                        value={selectedOperator || ""}
                        onValueChange={handleOperatorSelect}
                        disabled={isLoadingOperators}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue
                            placeholder={
                              isLoadingOperators
                                ? "Loading operators..."
                                : "Select an operator"
                            }
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {operators?.map((operator) => (
                            <SelectItem
                              key={operator.id}
                              value={operator.id}
                              disabled={!operator.products?.length}
                              className="flex justify-between items-center"
                            >
                              <span>{operator.name}</span>
                              {!operator.products?.length && (
                                <span className="text-xs text-muted-foreground ml-2">
                                  (No products)
                                </span>
                              )}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  </div>

                  {operators?.some((op) => op.products?.length > 0) ? (
                    <Button
                      className="w-full"
                      onClick={handlePurchase}
                      disabled={orderMutation.isPending || !selectedOperator}
                    >
                      {orderMutation.isPending
                        ? "Purchasing..."
                        : "Purchase Number"}
                    </Button>
                  ) : operators && operators.length > 0 ? (
                    <div className="text-sm text-yellow-600 bg-yellow-50 p-3 rounded-md border border-yellow-200">
                      <p className="font-medium">No products available</p>
                      <p className="text-xs mt-1">
                        There are no available products for the operators in
                        this country. Please try a different country or contact
                        support.
                      </p>
                    </div>
                  ) : null}
                </CardContent>
              </Card>
            )}
          </div>

          <div className="space-y-6">
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg">
                    <Smartphone className="w-5 h-5 text-white" />
                  </div>
                  Your Virtual Number
                </CardTitle>
              </CardHeader>
              <CardContent>
                <PhoneNumberDisplay
                  number={currentOrder?.phone || null}
                  isLoading={orderMutation.isPending}
                />
              </CardContent>
            </Card>

            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <div className="p-2 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg">
                    <RefreshCw className="w-5 h-5 text-white" />
                  </div>
                  SMS Messages
                  {messages.length > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {messages.length}
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SMSViewer
                  messages={messages}
                  isLoading={isLoadingMessages && !!currentOrder?.id}
                />
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Features Section */}
        <div className="mt-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">
              Why Choose Instant OTP World?
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Experience the fastest and most reliable virtual phone number
              service with advanced features
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center p-6 shadow-lg border-0 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 hover:shadow-xl transition-all duration-300">
              <CardContent className="pt-6">
                <div className="p-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl w-fit mx-auto mb-4">
                  <Globe className="h-8 w-8 text-white" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Global Coverage</h3>
                <p className="text-sm text-muted-foreground">
                  Access virtual numbers from 160+ countries worldwide
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-6 shadow-lg border-0 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 hover:shadow-xl transition-all duration-300">
              <CardContent className="pt-6">
                <div className="p-3 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl w-fit mx-auto mb-4">
                  <Zap className="h-8 w-8 text-white" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Lightning Fast</h3>
                <p className="text-sm text-muted-foreground">
                  Get your virtual number instantly in under 5 seconds
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-6 shadow-lg border-0 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 hover:shadow-xl transition-all duration-300">
              <CardContent className="pt-6">
                <div className="p-3 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl w-fit mx-auto mb-4">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <h3 className="font-semibold text-lg mb-2">100% Secure</h3>
                <p className="text-sm text-muted-foreground">
                  Your privacy is protected with enterprise-grade security
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-6 shadow-lg border-0 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 hover:shadow-xl transition-all duration-300">
              <CardContent className="pt-6">
                <div className="p-3 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl w-fit mx-auto mb-4">
                  <RefreshCw className="h-8 w-8 text-white" />
                </div>
                <h3 className="font-semibold text-lg mb-2">
                  Real-time Updates
                </h3>
                <p className="text-sm text-muted-foreground">
                  Receive SMS messages instantly with live updates
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Index;
