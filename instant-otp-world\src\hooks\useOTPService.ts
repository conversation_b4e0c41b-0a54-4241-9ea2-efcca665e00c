import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { AxiosError } from "axios";
import {
  fetchCountries,
  fetchOperators,
  createOrder,
  fetchMessages,
  type Country,
  type OrderResponse,
  type Operator,
  type Message,
} from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";

export const useOTPService = () => {
  const { toast } = useToast();
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [selectedOperator, setSelectedOperator] = useState<string | null>(null);
  const [currentOrder, setCurrentOrder] = useState<OrderResponse | null>(null);
  const queryClient = useQueryClient();

  // Fetch countries
  const { data: countries, isLoading: isLoadingCountries } = useQuery({
    queryKey: ["countries"],
    queryFn: fetchCountries,
  });

  // Fetch operators when country changes
  const { data: operators, isLoading: isLoadingOperators } = useQuery({
    queryKey: ["operators", selectedCountry],
    queryFn: () =>
      selectedCountry ? fetchOperators(selectedCountry) : Promise.resolve([]),
    enabled: !!selectedCountry,
  });

  // Fetch messages when there's an active order
  const { data: messages = [], isLoading: isLoadingMessages } = useQuery({
    queryKey: ["messages", currentOrder?.id],
    queryFn: () =>
      currentOrder?.id ? fetchMessages(currentOrder.id) : Promise.resolve([]),
    enabled: !!currentOrder?.id,
    refetchInterval: 5000, // Poll every 5 seconds
    refetchIntervalInBackground: true,
  });

  // Create order mutation
  const orderMutation = useMutation<
    OrderResponse,
    AxiosError,
    { country: string; operator: string; product: string }
  >({
    mutationFn: (params) => createOrder(params),
    onSuccess: (data) => {
      setCurrentOrder(data);
      toast({
        title: "Success",
        description: "Successfully purchased phone number",
      });
    },
    onError: (error: AxiosError) => {
      let errorMessage = "Failed to purchase phone number. Please try again.";
      
      if (
        error.response?.data &&
        typeof error.response.data === "object" &&
        "error" in error.response.data
      ) {
        errorMessage = (error.response.data as { error: string }).error;
      } else if (
        error.response?.data &&
        typeof error.response.data === "object" &&
        "message" in error.response.data
      ) {
        errorMessage = (error.response.data as { message: string }).message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const handleCountrySelect = (countryId: string) => {
    setSelectedCountry(countryId);
    setSelectedOperator(null);
    setCurrentOrder(null);
  };

  const handleOperatorSelect = (operatorId: string) => {
    setSelectedOperator(operatorId);
  };

  const handlePurchase = async () => {
    if (!selectedCountry || !selectedOperator) {
      toast({
        title: "Error",
        description: "Please select both country and operator",
        variant: "destructive",
      });
      return;
    }

    try {
      const operator = operators?.find((op) => op.id === selectedOperator);

      if (!operator || !operator.products?.length) {
        throw new Error("No products available for the selected operator");
      }

      const product = operator.products[0];

      const orderParams = {
        country: selectedCountry,
        operator: selectedOperator,
        product: product,
      };

      orderMutation.mutate(orderParams);
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to place order",
        variant: "destructive",
      });
    }
  };

  return {
    // State
    selectedCountry,
    selectedOperator,
    currentOrder,
    
    // Data
    countries,
    operators,
    messages,
    
    // Loading states
    isLoadingCountries,
    isLoadingOperators,
    isLoadingMessages,
    isPurchasing: orderMutation.isPending,
    
    // Actions
    handleCountrySelect,
    handleOperatorSelect,
    handlePurchase,
  };
};
