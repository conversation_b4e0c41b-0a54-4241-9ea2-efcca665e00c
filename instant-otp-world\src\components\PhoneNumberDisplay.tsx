import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Phone, Copy, CheckCircle, Loader2, Clock } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";

interface PhoneNumberDisplayProps {
  number: string | null;
  isLoading: boolean;
}

export const PhoneNumberDisplay = ({
  number,
  isLoading,
}: PhoneNumberDisplayProps) => {
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  const handleCopy = async () => {
    if (!number) return;

    try {
      await navigator.clipboard.writeText(number);
      setCopied(true);
      toast({
        title: "Copied!",
        description: "Phone number copied to clipboard",
      });
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast({
        title: "Failed to copy",
        description: "Please copy the number manually",
        variant: "destructive",
      });
    }
  };

  return (
    <div>
      {!number && !isLoading ? (
        <div className="text-center py-12">
          <div className="p-4 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-2xl w-fit mx-auto mb-6">
            <Phone className="w-12 h-12 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold mb-2">No Number Selected</h3>
          <p className="text-muted-foreground">
            Select a country and operator to get your virtual number
          </p>
        </div>
      ) : isLoading ? (
        <div className="text-center py-12">
          <div className="p-4 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl w-fit mx-auto mb-6">
            <Loader2 className="w-12 h-12 text-blue-600 animate-spin" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Getting Your Number</h3>
          <p className="text-muted-foreground">
            Please wait while we purchase your virtual number...
          </p>
        </div>
      ) : number ? (
        <div className="space-y-6">
          {/* Number Display */}
          <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg">
                  <Phone className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-green-800 dark:text-green-200">
                    Your Virtual Number
                  </h3>
                  <p className="text-sm text-green-600 dark:text-green-400">
                    Ready to receive SMS
                  </p>
                </div>
              </div>
              <Badge className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800">
                Active
              </Badge>
            </div>

            <div className="bg-white/80 dark:bg-gray-800/80 rounded-xl p-4 mb-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm text-muted-foreground mb-1">
                    Phone Number:
                  </p>
                  <p className="text-2xl font-mono font-bold tracking-wider text-gray-900 dark:text-gray-100 break-all">
                    {number}
                  </p>
                </div>
                <Button
                  onClick={handleCopy}
                  variant="outline"
                  size="sm"
                  className={`ml-4 transition-all duration-200 ${
                    copied
                      ? "bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
                      : "hover:bg-gray-50"
                  }`}
                >
                  {copied ? (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                      Copied!
                    </>
                  ) : (
                    <>
                      <Copy className="w-4 h-4 mr-2" />
                      Copy
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Timer and Status */}
            <div className="flex items-center justify-center gap-2 text-sm text-green-700 dark:text-green-300">
              <Clock className="w-4 h-4" />
              <span>Active for 20 minutes • SMS will appear below</span>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4 border border-blue-200/50 dark:border-blue-800/50">
            <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
              How to use:
            </h4>
            <ol className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <li>1. Copy the phone number above</li>
              <li>2. Use it for verification on your desired service</li>
              <li>3. SMS messages will appear in the Messages section below</li>
              <li>4. The number expires in 20 minutes</li>
            </ol>
          </div>
        </div>
      ) : null}
    </div>
  );
};
