import axios from "axios";

const API_BASE_URL = "http://localhost:3001/api";

const api = axios.create({
  baseURL: API_BASE_URL,
});

export interface Country {
  id: string;
  name: string;
  available: number;
  iso: string;
  prefix: string;
}

export interface Operator {
  id: string;
  name: string;
  products: string[];
}

export interface OrderResponse {
  id: string;
  phone: string;
  status: string;
}

export interface Message {
  id: string;
  code: string;
  text: string;
  sender: string;
  time: string;
}

export interface MessagesResponse {
  sms?: Array<{
    id: string;
    text: string;
    code: string;
    sender: string;
    date: string;
  }>;
  status: string;
}

export const fetchCountries = async (): Promise<Record<string, Country>> => {
  const response = await api.get("/countries");
  return response.data;
};

export const fetchOperators = async (country: string): Promise<Operator[]> => {
  try {
    const response = await api.get(`/operators/${country}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      // Silent error handling - could add proper error reporting here
    }
    throw error;
  }
};

export interface CreateOrderParams {
  country: string;
  operator: string;
  product: string;
}

export const createOrder = async ({
  country,
  operator,
  product,
}: CreateOrderParams): Promise<OrderResponse> => {
  const response = await api.post("/order", {
    country,
    operator,
    product,
  });

  return response.data;
};

export const fetchMessages = async (orderId: string): Promise<Message[]> => {
  try {
    const response = await api.get(`/messages/${orderId}`);

    // Transform the 5sim API response to our Message format
    const messagesData: MessagesResponse = response.data;

    if (!messagesData.sms || !Array.isArray(messagesData.sms)) {
      return [];
    }

    const messages: Message[] = messagesData.sms.map((sms, index) => ({
      id: sms.id || `msg-${index}`,
      code: sms.code || extractCodeFromText(sms.text),
      text: sms.text || "",
      sender: sms.sender || "Unknown",
      time: sms.date || new Date().toISOString(),
    }));

    return messages;
  } catch (error) {
    // Don't throw error for messages - just return empty array
    return [];
  }
};

// Helper function to extract verification code from SMS text
const extractCodeFromText = (text: string): string => {
  if (!text) return "";

  // Common patterns for verification codes
  const patterns = [
    /\b(\d{4,8})\b/, // 4-8 digit codes
    /code[:\s]*(\d+)/i, // "code: 123456"
    /verification[:\s]*(\d+)/i, // "verification: 123456"
    /otp[:\s]*(\d+)/i, // "otp: 123456"
  ];

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match) {
      return match[1];
    }
  }

  // If no pattern matches, return the first sequence of digits
  const digitMatch = text.match(/\d+/);
  return digitMatch ? digitMatch[0] : "";
};
