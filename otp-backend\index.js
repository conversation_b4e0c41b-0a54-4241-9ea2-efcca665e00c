const express = require("express");
const dotenv = require("dotenv");
const axios = require("axios");
const cors = require("cors");

// Load environment variables
dotenv.config();

const app = express();
app.use(cors());
app.use(express.json());
const PORT = process.env.PORT || 3000;
const API_KEY = process.env.FIVESIM_API_KEY;
const BASE_URL = "https://5sim.net/v1";

// Validate API key on startup
if (!API_KEY) {
  console.error("❌ FIVESIM_API_KEY is not set in environment variables");
  process.exit(1);
}

console.log(
  "🔑 API Key loaded:",
  API_KEY ? `Yes (${API_KEY.length} characters)` : "No"
);

// Create axios instance with default config
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    Authorization: `Bearer ${API_KEY}`,
    Accept: "application/json",
  },
});

// Test API key validity on startup
async function validateApi<PERSON>ey() {
  try {
    console.log("🔍 Testing API key validity...");
    const response = await api.get("/user/profile");
    console.log("✅ API Key is valid!");
    console.log("📊 Account info:", {
      email: response.data.email,
      balance: response.data.balance,
      rating: response.data.rating,
      vendor: response.data.vendor,
      frozen_balance: response.data.frozen_balance,
    });

    // Check account requirements and provide specific guidance
    let hasIssues = false;

    if (response.data.balance <= 0) {
      console.error("❌ CRITICAL: Account balance is 0 or negative!");
      console.error(
        "   → You CANNOT purchase numbers without sufficient balance."
      );
      console.error(
        "   → Please add funds to your 5sim account at: https://5sim.net/settings/payments"
      );
      hasIssues = true;
    }

    if (response.data.rating < 1) {
      console.error("❌ CRITICAL: Account rating is below 1!");
      console.error(
        "   → 5sim requires a minimum rating of 1 to purchase numbers."
      );
      console.error(
        "   → Rating improves with successful purchases and account activity."
      );
      hasIssues = true;
    }

    if (response.data.frozen_balance > 0) {
      console.warn(
        "⚠️  WARNING: You have frozen balance:",
        response.data.frozen_balance
      );
      console.warn("   → This may indicate account restrictions.");
    }

    if (hasIssues) {
      console.error(
        "🚨 ACCOUNT ISSUES DETECTED - Number purchases will fail with 401 errors!"
      );
      console.error(
        "   → Please resolve the above issues in your 5sim account."
      );
      console.error("   → Visit: https://5sim.net/settings");
    } else {
      console.log("✅ Account appears ready for number purchases!");
    }
  } catch (error) {
    console.error("❌ API Key validation failed:");
    console.error("Status:", error.response?.status);
    console.error("Status Text:", error.response?.statusText);
    console.error("Error Data:", JSON.stringify(error.response?.data, null, 2));

    if (error.response?.status === 401) {
      console.error("🔍 401 Error Analysis:");
      console.error("   → Your API key may be invalid, expired, or revoked");
      console.error("   → Your account may be suspended or banned");
      console.error("   → Your account may not be verified");
      console.error(
        "   → Please check your 5sim account status at: https://5sim.net/settings"
      );
      console.error("   → Consider generating a new API key if needed");
    }

    console.error(
      "⚠️  This WILL cause 401 errors when trying to purchase numbers."
    );
  }
}

// Call validation function
validateApiKey();

// GET /api/countries
app.get("/api/countries", async (req, res) => {
  try {
    const response = await api.get("/guest/countries");
    const countriesData = response.data;

    // Transform the data while keeping it as an object
    const transformedCountries = Object.entries(countriesData).reduce(
      (acc, [countryCode, data]) => {
        // Extract the count from the virtual21 property if it exists
        const count = data.virtual21?.count || 0;

        acc[countryCode] = {
          id: countryCode,
          name: data.text_en || "Unknown",
          available: count,
          iso: data.iso?.code || countryCode,
          prefix: data.prefix?.code || "",
        };
        return acc;
      },
      {}
    );

    res.json(transformedCountries);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch countries" });
  }
});

// GET /api/operators/:country
app.get("/api/operators/:country", async (req, res) => {
  const { country } = req.params;
  try {
    const response = await api.get(`/guest/prices?country=${country}`);
    const countryData = response.data[country] || {};

    // Transform the object into an array of operators
    const operators = Object.entries(countryData).map(([operatorId, data]) => {
      // The data structure from 5sim has virtual operators as keys
      // Each virtual operator contains product information
      const virtualOperators = Object.keys(data || {});
      const hasProducts = virtualOperators.length > 0;

      const operator = {
        id: operatorId,
        name: operatorId.charAt(0).toUpperCase() + operatorId.slice(1), // Capitalize first letter
        products: hasProducts ? ["virtual"] : [], // Simplified - just indicate if products are available
      };
      return operator;
    });

    res.json(operators);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch operators and products" });
  }
});

// POST /api/order
app.post("/api/order", async (req, res) => {
  const { country, operator, product } = req.body;

  try {
    let operatorId = operator;
    let productId = product;

    // Always get the pricing data to find the correct virtual operator
    const operatorsResponse = await api.get(`/guest/prices?country=${country}`);
    const countryData = operatorsResponse.data[country] || {};

    // If operator not specified, get the first available one
    if (!operatorId) {
      const firstOperator = Object.entries(countryData)[0];
      if (!firstOperator) {
        return res
          .status(400)
          .json({ error: "No operators available for this country" });
      }
      operatorId = firstOperator[0];
    }

    // Get the operator data
    const operatorData = countryData[operatorId];
    if (!operatorData) {
      return res
        .status(400)
        .json({ error: `Operator ${operatorId} not available for ${country}` });
    }

    // Get the first virtual operator (like virtual21, virtual34, etc.)
    const virtualOperators = Object.keys(operatorData || {});
    if (virtualOperators.length === 0) {
      return res
        .status(400)
        .json({ error: "No virtual operators available for this operator" });
    }

    // Use the first available virtual operator as the product
    productId = virtualOperators[0];
    console.log("DEBUG: Selected virtual operator (product):", productId);

    // Create order with specified or first available operator and product
    const response = await api.get(
      `/user/buy/activation/${country}/${operatorId}/${productId}`
    );

    // Transform the response to match frontend expectations
    const transformedResponse = {
      id: response.data.id?.toString() || "",
      phone: response.data.phone || "",
      status: response.data.status || "pending",
      // Include original data for debugging
      original: response.data,
    };

    res.json(transformedResponse);
  } catch (error) {
    // Enhanced error logging for debugging
    console.error("❌ Order creation failed:");
    console.error("Request URL:", error.config?.url);
    console.error("Request method:", error.config?.method);
    console.error("Request headers:", error.config?.headers);

    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error("Response status:", error.response.status);
      console.error(
        "Response data:",
        JSON.stringify(error.response.data, null, 2)
      );
      console.error("Response headers:", error.response.headers);

      let errorMessage = "Failed to buy phone number";
      let debugInfo = {
        status: error.response.status,
        data: error.response.data,
        timestamp: new Date().toISOString(),
      };

      if (error.response.status === 401) {
        errorMessage = `❌ Account Authentication Failed

Your 5sim account cannot purchase numbers. This is usually caused by:

🔸 Insufficient Balance: Your account balance is $0 or negative
   → Add funds at: https://5sim.net/settings/payments

🔸 Low Rating: Your account rating is below 1 (minimum required)
   → Rating improves with successful purchases and account activity

🔸 Account Not Verified: Your account may need verification
   → Check account status at: https://5sim.net/settings

🔸 Invalid/Expired API Key: Your API token may be revoked
   → Generate a new API key if needed

Please resolve these issues in your 5sim account before trying again.`;

        // Add specific debugging for 401 errors
        console.error("🔍 401 Error Debug Info:");
        console.error(
          "- API Key length:",
          API_KEY ? API_KEY.length : "Not set"
        );
        console.error(
          "- API Key starts with:",
          API_KEY ? API_KEY.substring(0, 20) + "..." : "N/A"
        );
        console.error(
          "- Authorization header:",
          error.config?.headers?.Authorization ? "Present" : "Missing"
        );
      } else if (error.response.status === 400) {
        // Check specific 400 error messages from 5sim
        const errorData = error.response.data;
        if (typeof errorData === 'string' && errorData.includes('not enough user balance')) {
          errorMessage = `💰 Insufficient Account Balance

Your 5sim account balance is too low to purchase this number.

🔸 Current issue: Not enough funds in your account
🔸 Solution: Add money to your 5sim account
🔸 Visit: https://5sim.net/settings/payments

After adding funds, try purchasing the number again.`;
        } else if (typeof errorData === 'string' && errorData.includes('not enough rating')) {
          errorMessage = `⭐ Account Rating Too Low

Your 5sim account rating is below the minimum required.

🔸 Current issue: Rating below minimum threshold
🔸 Solution: Improve your account rating through successful purchases
🔸 Visit: https://5sim.net/settings

Rating improves with account activity and successful transactions.`;
        } else {
          errorMessage =
            "Invalid request parameters. The selected country/operator combination may not be available.";
        }
      } else if (error.response.status === 403) {
        errorMessage =
          "Access forbidden. Your account may not have permission to purchase numbers.";
      }

      res.status(error.response.status).json({
        error: errorMessage,
        details: error.response.data,
        debug: debugInfo,
      });
    } else if (error.request) {
      // The request was made but no response was received
      console.error("No response received from 5sim API");
      console.error("Request details:", error.request);
      res.status(500).json({
        error: "No response received from 5sim API",
        debug: { timestamp: new Date().toISOString() },
      });
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error("Request setup error:", error.message);
      res.status(500).json({
        error: "Failed to make request to 5sim API",
        debug: {
          message: error.message,
          timestamp: new Date().toISOString(),
        },
      });
    }
  }
});

// GET /api/messages/:id
app.get("/api/messages/:id", async (req, res) => {
  const { id } = req.params;
  try {
    const response = await api.get(`/user/check/${id}`);
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch messages" });
  }
});

app.listen(PORT, () => {
  // Server started
});
