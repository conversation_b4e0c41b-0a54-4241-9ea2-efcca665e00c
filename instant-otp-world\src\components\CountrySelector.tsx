import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Globe, Search, MapPin } from "lucide-react";
import { type Country } from "@/lib/api";
import { useState, useMemo } from "react";

interface CountrySelectorProps {
  countries: Record<string, Country>;
  selectedCountry: string | null;
  onSelect: (countryId: string) => void;
  isLoading: boolean;
}

export const CountrySelector = ({
  countries,
  selectedCountry,
  onSelect,
  isLoading,
}: CountrySelectorProps) => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredCountries = useMemo(() => {
    const countryEntries = Object.entries(countries);
    if (!searchTerm) return countryEntries;

    return countryEntries.filter(
      ([code, country]) =>
        country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        country.prefix.includes(searchTerm)
    );
  }, [countries, searchTerm]);

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-10 bg-muted animate-pulse rounded-lg" />
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-muted animate-pulse rounded-lg" />
          ))}
        </div>
      </div>
    );
  }

  const countryEntries = Object.entries(countries);

  if (countryEntries.length === 0) {
    return (
      <div className="text-center py-12">
        <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">No countries available</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search countries..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 bg-white/50 border-gray-200 focus:bg-white transition-colors"
        />
      </div>

      {/* Countries List */}
      <div className="space-y-2 max-h-80 overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
        {filteredCountries.length === 0 ? (
          <div className="text-center py-8">
            <Search className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">No countries found</p>
          </div>
        ) : (
          filteredCountries.map(([code, country]) => (
            <Button
              key={code}
              variant={selectedCountry === code ? "default" : "outline"}
              className={`w-full justify-between h-auto p-4 transition-all duration-200 hover:shadow-md ${
                selectedCountry === code
                  ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0 shadow-lg"
                  : "bg-white/70 hover:bg-white border-gray-200"
              }`}
              onClick={() => onSelect(code)}
            >
              <div className="flex items-center gap-3">
                <div
                  className={`p-1.5 rounded-lg ${
                    selectedCountry === code
                      ? "bg-white/20"
                      : "bg-gradient-to-r from-blue-500 to-purple-600"
                  }`}
                >
                  <MapPin
                    className={`h-3 w-3 ${
                      selectedCountry === code ? "text-white" : "text-white"
                    }`}
                  />
                </div>
                <div className="text-left">
                  <div className="font-medium">{country.name}</div>
                  <div
                    className={`text-xs ${
                      selectedCountry === code
                        ? "text-white/80"
                        : "text-muted-foreground"
                    }`}
                  >
                    {country.prefix} • {code.toUpperCase()}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge
                  variant={selectedCountry === code ? "secondary" : "outline"}
                  className={`${
                    selectedCountry === code
                      ? "bg-white/20 text-white border-white/30"
                      : "bg-green-50 text-green-700 border-green-200"
                  }`}
                >
                  {country.available} available
                </Badge>
              </div>
            </Button>
          ))
        )}
      </div>
    </div>
  );
};
